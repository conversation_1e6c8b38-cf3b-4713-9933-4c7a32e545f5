# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.


ActiveRecord::Schema[8.0].define(version: 2025_08_13_155311) do
  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "appraisals", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "dealership_id", null: false
    t.bigint "customer_id", null: false
    t.bigint "sales_person_id"
    t.bigint "created_by_id"
    t.bigint "updated_by_id"
    t.integer "status", default: 0, null: false
    t.integer "completed_percentage"
    t.decimal "awarded_value", precision: 15, scale: 2
    t.decimal "price", precision: 15, scale: 2
    t.decimal "give_price", precision: 15, scale: 2
    t.string "awarded_notes", limit: 1000
    t.string "notes", limit: 1000
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_appraisals_on_created_by_id"
    t.index ["customer_id"], name: "index_appraisals_on_customer_id"
    t.index ["dealership_id"], name: "index_appraisals_on_dealership_id"
    t.index ["sales_person_id"], name: "index_appraisals_on_sales_person_id"
    t.index ["updated_by_id"], name: "index_appraisals_on_updated_by_id"
    t.index ["uuid"], name: "index_appraisals_on_uuid", unique: true
  end

  create_table "body_part_conditions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "vehicle_condition_id", null: false
    t.integer "part_name", null: false
    t.string "condition", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["vehicle_condition_id"], name: "index_body_part_conditions_on_vehicle_condition_id"
  end

  create_table "brands", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "uuid", limit: 36, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_brands_on_name", unique: true
    t.index ["uuid"], name: "index_brands_on_uuid", unique: true
  end

  create_table "component_ratings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "name", null: false
    t.integer "rating", null: false
    t.bigint "vehicle_condition_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["vehicle_condition_id"], name: "index_component_ratings_on_vehicle_condition_id"
  end

  create_table "customer_vehicles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "customer_id", null: false
    t.bigint "dealership_id", null: false
    t.string "make", null: false
    t.string "model", null: false
    t.string "rego", limit: 20
    t.string "vin", limit: 17
    t.integer "build_year", null: false
    t.integer "build_month"
    t.boolean "is_vehicle_present", default: true, null: false
    t.string "registration_state", limit: 10
    t.integer "compliance_month"
    t.integer "compliance_year"
    t.date "odometer_date"
    t.integer "odometer_reading"
    t.string "exterior_color", limit: 45
    t.string "interior_color", limit: 45
    t.integer "seat_type", limit: 1, default: 0
    t.date "registration_expiry"
    t.string "engine_size", limit: 20
    t.string "engine_number", limit: 50
    t.integer "fuel_type", limit: 1, default: 0
    t.integer "driving_wheels", limit: 1, default: 0
    t.string "wheel_size", limit: 20
    t.integer "spare_wheel_type", limit: 1, default: 0
    t.integer "transmission", limit: 1, default: 0
    t.integer "number_of_doors", limit: 2
    t.integer "number_of_seats", limit: 2
    t.integer "body_type", limit: 1, default: 0
    t.string "redbook_code", limit: 50
    t.bigint "brand_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "engine_kilowatts"
    t.integer "wheel_size_front"
    t.integer "wheel_size_rear"
    t.bigint "appraisal_id"
    t.index ["appraisal_id"], name: "index_customer_vehicles_on_appraisal_id"
    t.index ["brand_id"], name: "index_customer_vehicles_on_brand_id"
    t.index ["customer_id", "rego"], name: "index_customer_vehicles_on_customer_rego"
    t.index ["customer_id"], name: "index_customer_vehicles_on_customer_id"
    t.index ["dealership_id", "make", "model"], name: "index_customer_vehicles_on_dealership_make_model"
    t.index ["dealership_id"], name: "index_customer_vehicles_on_dealership_id"
    t.index ["is_vehicle_present"], name: "index_customer_vehicles_on_vehicle_present"
    t.index ["rego"], name: "index_customer_vehicles_on_rego"
    t.index ["uuid"], name: "index_customer_vehicles_on_uuid", unique: true
    t.index ["vin"], name: "index_customer_vehicles_on_vin"
  end

  create_table "customers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.integer "age", limit: 2
    t.string "phone_number", limit: 20
    t.string "email"
    t.integer "gender", limit: 1
    t.string "postcode", limit: 10
    t.string "suburb", limit: 100
    t.string "address_line1"
    t.string "address_line2"
    t.string "city", limit: 100
    t.string "state", limit: 100
    t.string "country", limit: 100, default: "au", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "company_name"
    t.string "external_id"
    t.string "uuid", limit: 36
    t.index ["dealership_id", "email"], name: "index_customers_on_dealership_email"
    t.index ["dealership_id", "first_name"], name: "index_customers_on_dealership_first_name"
    t.index ["dealership_id", "last_name"], name: "index_customers_on_dealership_last_name"
    t.index ["dealership_id", "phone_number"], name: "index_customers_on_dealership_phone_number"
    t.index ["dealership_id"], name: "index_customers_on_dealership_id"
    t.index ["email", "dealership_id"], name: "index_customers_on_email_and_dealership_id", unique: true
    t.index ["email"], name: "index_customers_on_email"
    t.index ["first_name", "last_name"], name: "index_customers_on_full_name"
    t.index ["first_name"], name: "index_customers_on_first_name"
    t.index ["last_name"], name: "index_customers_on_last_name"
    t.index ["phone_number"], name: "index_customers_on_phone_number"
    t.index ["uuid"], name: "index_customers_on_uuid", unique: true
  end

  create_table "damage_reports", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "drive_id"
    t.integer "report_type"
    t.bigint "vehicle_id"
    t.string "uuid", limit: 36
    t.index ["drive_id"], name: "index_damage_reports_on_drive_id"
    t.index ["uuid"], name: "index_damage_reports_on_uuid", unique: true
    t.index ["vehicle_id"], name: "index_damage_reports_on_vehicle_id"
  end

  create_table "dealership_alerts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.integer "alert_type"
    t.integer "threshold"
    t.string "emails"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dealership_id"], name: "index_dealership_alerts_on_dealership_id"
  end

  create_table "dealership_email_settings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.boolean "send_test_drive_review_email", default: false, null: false
    t.boolean "send_test_drive_terms_email", default: false, null: false
    t.boolean "send_loan_terms_email", default: false, null: false
    t.integer "level1_odometer_warning_km"
    t.string "level1_odometer_warning_email"
    t.integer "level2_odometer_warning_km"
    t.string "level2_odometer_warning_email"
    t.string "email_from_address"
    t.string "email_display_name"
    t.boolean "loan_review_email_enabled", default: false, null: false
    t.integer "send_email_for_bookings", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dealership_id"], name: "index_dealership_email_settings_on_dealership_id"
  end

  create_table "dealership_features_settings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.boolean "advance_booking_enabled", default: false, null: false
    t.boolean "insurance_waiver_enabled", default: false, null: false
    t.boolean "dealer_drive_subscription", default: false, null: false
    t.boolean "appraisals_subscription", default: false, null: false
    t.integer "setting_recent_customer_age"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "fuel_level_in_test_drive", default: false, null: false
    t.boolean "fuel_level_in_loan", default: false, null: false
    t.index ["dealership_id"], name: "index_dealership_features_settings_on_dealership_id"
  end

  create_table "dealership_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.string "signup_code"
    t.integer "status", default: 0, null: false
    t.datetime "contract_end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_dealership_groups_on_name"
  end

  create_table "dealership_terms_settings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.text "test_drive_terms_text"
    t.text "car_loan_terms_text"
    t.text "insurance_waiver_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dealership_id"], name: "index_dealership_terms_settings_on_dealership_id"
  end

  create_table "dealership_vehicles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.string "vin", limit: 50
    t.string "stock_number", limit: 50
    t.string "rego", limit: 50
    t.string "make", null: false
    t.string "model", null: false
    t.string "color", limit: 45
    t.bigint "build_year", unsigned: true
    t.date "rego_expiry"
    t.boolean "is_trade_plate_used", default: false, null: false
    t.integer "last_known_odometer_km"
    t.integer "last_known_fuel_gauge_level", default: -1
    t.datetime "last_system_inspection_timestamp"
    t.integer "status", limit: 3, null: false, unsigned: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "available_for_drive", default: false, null: false
    t.integer "vehicle_type"
    t.string "uuid", limit: 36
    t.bigint "brand_id"
    t.index ["brand_id"], name: "index_dealership_vehicles_on_brand_id"
    t.index ["dealership_id", "stock_number"], name: "idx_dealership_stock_number"
    t.index ["dealership_id"], name: "index_dealership_vehicles_on_dealership_id"
    t.index ["rego"], name: "idx_vehicles_on_rego"
    t.index ["uuid"], name: "index_dealership_vehicles_on_uuid", unique: true
  end

  create_table "dealerships", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.integer "status", default: 0, null: false
    t.string "address_line1", null: false
    t.string "address_line2"
    t.string "suburb"
    t.string "state", null: false
    t.string "postcode", null: false
    t.string "country", default: "au", null: false
    t.string "phone", null: false
    t.string "email", null: false
    t.integer "setting_date_format", default: 0, null: false
    t.string "setting_time_zone", default: "UTC", null: false
    t.integer "setting_distance_unit", default: 0, null: false
    t.string "external_id"
    t.string "abn"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "dealership_group_id"
    t.string "xero_id"
    t.string "long_name"
    t.string "uuid", limit: 36
    t.bigint "brand_id"
    t.string "website"
    t.index ["brand_id"], name: "index_dealerships_on_brand_id"
    t.index ["dealership_group_id"], name: "index_dealerships_on_dealership_group_id"
    t.index ["external_id"], name: "index_dealerships_on_external_id"
    t.index ["name"], name: "index_dealerships_on_name"
    t.index ["setting_time_zone"], name: "index_dealerships_on_setting_time_zone"
    t.index ["uuid"], name: "index_dealerships_on_uuid", unique: true
  end

  create_table "device_registrations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "device_id", null: false
    t.string "fcm_token"
    t.string "device_name"
    t.integer "device_os", null: false
    t.string "device_os_version"
    t.string "app_version", null: false
    t.string "app_build_number", null: false
    t.string "last_login_ip"
    t.datetime "last_login_timestamp"
    t.string "refresh_token", null: false
    t.datetime "refresh_token_expires_at", null: false
    t.boolean "active", default: true, null: false
    t.datetime "logged_out_at"
    t.datetime "last_activity_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uuid", limit: 36
    t.index ["refresh_token"], name: "index_device_registrations_on_refresh_token", unique: true
    t.index ["user_id", "device_id"], name: "index_device_registrations_on_user_id_and_device_id", unique: true
    t.index ["user_id"], name: "index_device_registrations_on_user_id"
    t.index ["uuid"], name: "index_device_registrations_on_uuid", unique: true
  end

  create_table "driver_licenses", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "holder_type", null: false
    t.bigint "holder_id", null: false
    t.string "licence_number", limit: 20
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "expiry_date"
    t.string "issuing_country", limit: 100, default: "au"
    t.string "issuing_state", limit: 100
    t.string "category", limit: 20
    t.string "full_name"
    t.date "date_of_birth"
    t.date "issue_date"
    t.integer "verification_status", limit: 1, unsigned: true
    t.string "verification_rejection_reason"
    t.string "uuid", limit: 36
    t.index ["holder_type", "holder_id"], name: "index_driver_licenses_on_holder"
    t.index ["uuid"], name: "index_driver_licenses_on_uuid", unique: true
  end

  create_table "drives", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.bigint "vehicle_id", null: false
    t.bigint "customer_id"
    t.bigint "sales_person_id", null: false
    t.bigint "driver_license_id"
    t.integer "drive_type", limit: 1, null: false, unsigned: true
    t.integer "status", limit: 1, default: 0, null: false, unsigned: true
    t.datetime "expected_pickup_datetime"
    t.datetime "expected_return_datetime"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_datetime"
    t.datetime "end_datetime"
    t.integer "start_odometer_reading"
    t.integer "end_odometer_reading"
    t.bigint "sales_person_accompanying_id"
    t.integer "sold_status", limit: 1
    t.string "uuid", limit: 36
    t.bigint "trade_plate_id"
    t.string "cancel_reason"
    t.datetime "cancelled_at"
    t.integer "start_fuel_gauge_level"
    t.integer "end_fuel_gauge_level"
    t.index ["customer_id"], name: "index_drives_on_customer_id"
    t.index ["dealership_id", "status", "id"], name: "idx_dealership_status_id"
    t.index ["dealership_id"], name: "index_drives_on_dealership_id"
    t.index ["driver_license_id"], name: "index_drives_on_driver_license_id"
    t.index ["sales_person_accompanying_id"], name: "index_drives_on_sales_person_accompanying_id"
    t.index ["sales_person_id"], name: "index_drives_on_sales_person_id"
    t.index ["trade_plate_id"], name: "index_drives_on_trade_plate_id"
    t.index ["uuid"], name: "index_drives_on_uuid", unique: true
    t.index ["vehicle_id"], name: "index_drives_on_vehicle_id"
  end

  create_table "finance_details", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "customer_vehicle_id", null: false
    t.boolean "is_financed", default: false, null: false
    t.boolean "has_clear_title", default: false, null: false
    t.decimal "current_repayment_amount", precision: 10, scale: 2
    t.integer "terms_months"
    t.decimal "interest_rate", precision: 5, scale: 2
    t.date "next_due_date"
    t.string "finance_company", limit: 50
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "payout_amount", precision: 15, scale: 2
    t.index ["customer_vehicle_id"], name: "index_finance_details_on_customer_vehicle_id"
    t.index ["uuid"], name: "index_finance_details_on_uuid", unique: true
  end

  create_table "gps_locations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.decimal "latitude", precision: 10, scale: 6, null: false
    t.decimal "longitude", precision: 10, scale: 6, null: false
    t.decimal "accuracy", precision: 10, scale: 2
    t.string "trackable_type"
    t.bigint "trackable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["trackable_type", "trackable_id"], name: "index_gps_locations_on_trackable"
  end

  create_table "jwt_denylist", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "jti", null: false
    t.datetime "exp", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti"], name: "index_jwt_denylist_on_jti"
  end

  create_table "options_fitted", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "customer_vehicle_id", null: false
    t.boolean "has_sunroof", default: false, null: false
    t.boolean "has_tinted_windows", default: false, null: false
    t.boolean "has_towbar", default: false, null: false
    t.boolean "has_keyless_entry", default: false, null: false
    t.boolean "has_bluetooth", default: false, null: false
    t.boolean "has_ventilated_seats", default: false, null: false
    t.boolean "has_tray_fitted", default: false, null: false
    t.boolean "has_canopy_fitted", default: false, null: false
    t.boolean "has_aftermarket_wheels", default: false, null: false
    t.boolean "has_bull_bar", default: false, null: false
    t.boolean "has_extended_warranty", default: false, null: false
    t.date "extended_warranty_expiry"
    t.boolean "ppsr", default: false, null: false
    t.json "additional_options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "sunroof_type"
    t.integer "number_of_keys"
    t.boolean "heated_seats", default: false, null: false
    t.boolean "cargo_blind", default: false, null: false
    t.boolean "tonneau_cover", default: false, null: false
    t.integer "tonneau_type"
    t.integer "on_written_off_register"
    t.date "last_ppsr_date"
    t.index ["customer_vehicle_id"], name: "index_options_fitted_on_customer_vehicle_id", unique: true
    t.index ["uuid"], name: "index_options_fitted_on_uuid", unique: true
  end

  create_table "reconditioning_costs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "vehicle_condition_id", null: false
    t.integer "cost_type", null: false
    t.integer "amount_cents", null: false
    t.string "currency", default: "AUD", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["vehicle_condition_id"], name: "index_reconditioning_costs_on_vehicle_condition_id"
  end

  create_table "trade_plates", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "dealership_id", null: false
    t.string "number", null: false
    t.date "expiry"
    t.string "uuid", limit: 36, null: false
    t.integer "status", limit: 3, null: false, unsigned: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dealership_id", "number"], name: "index_trade_plates_on_dealership_id_and_number", unique: true
    t.index ["dealership_id"], name: "index_trade_plates_on_dealership_id"
    t.index ["uuid"], name: "index_trade_plates_on_uuid", unique: true
  end

  create_table "user_dealerships", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "dealership_id", null: false
    t.integer "role", limit: 1, default: 1, null: false, unsigned: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dealership_id"], name: "index_user_dealerships_on_dealership_id"
    t.index ["user_id", "dealership_id"], name: "index_user_dealerships_on_user_id_and_dealership_id", unique: true
    t.index ["user_id"], name: "index_user_dealerships_on_user_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "uuid", limit: 36
    t.integer "otp"
    t.datetime "otp_generated_at"
    t.integer "preferred_2fa", limit: 1, default: 2, null: false
    t.string "phone"
    t.integer "password_reset_code"
    t.datetime "reset_code_generated_at"
    t.integer "status", limit: 1, default: 0, null: false, unsigned: true
    t.integer "user_type", limit: 1, default: 0, null: false, unsigned: true
    t.string "last_login_ip"
    t.datetime "last_login_timestamp"
    t.string "job_title"
    t.string "delete_reason"
    t.integer "preferred_language", limit: 1, default: 0, null: false, unsigned: true
    t.string "external_id"
    t.string "time_zone", default: "UTC", null: false
    t.boolean "password_change_required", default: true, null: false
    t.boolean "onboarding_completed", default: false, null: false
    t.integer "otp_resend_count", limit: 1, default: 0, null: false, unsigned: true
    t.integer "reset_code_resend_count", limit: 1, default: 0, null: false, unsigned: true
    t.string "totp_secret"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["external_id"], name: "index_users_on_external_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["uuid"], name: "index_users_on_uuid", unique: true
  end

  create_table "vehicle_conditions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "customer_vehicle_id", null: false
    t.boolean "is_clean", default: true, null: false
    t.boolean "is_wet", default: false, null: false
    t.boolean "is_road_tested", default: false, null: false
    t.boolean "has_signs_of_repair", default: false, null: false
    t.string "repair_details"
    t.string "additional_notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_vehicle_id"], name: "index_vehicle_conditions_on_customer_vehicle_id"
    t.index ["uuid"], name: "index_vehicle_conditions_on_uuid", unique: true
  end

  create_table "vehicle_histories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "uuid", limit: 36, null: false
    t.bigint "customer_vehicle_id", null: false
    t.integer "number_of_owners"
    t.boolean "has_accident_history", default: false, null: false
    t.text "accident_details"
    t.date "last_service_date"
    t.integer "last_service_odometer"
    t.date "next_service_due"
    t.boolean "has_dash_warning_lights", default: false, null: false
    t.text "dash_warning_details"
    t.text "notes"
    t.integer "vehicle_history_status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_vehicle_id"], name: "index_vehicle_histories_on_customer_vehicle_id"
    t.index ["uuid"], name: "index_vehicle_histories_on_uuid", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "appraisals", "customers"
  add_foreign_key "appraisals", "dealerships"
  add_foreign_key "appraisals", "users", column: "created_by_id"
  add_foreign_key "appraisals", "users", column: "sales_person_id"
  add_foreign_key "appraisals", "users", column: "updated_by_id"
  add_foreign_key "body_part_conditions", "vehicle_conditions"
  add_foreign_key "component_ratings", "vehicle_conditions"
  add_foreign_key "customer_vehicles", "appraisals"
  add_foreign_key "customer_vehicles", "brands"
  add_foreign_key "customer_vehicles", "customers"
  add_foreign_key "customer_vehicles", "dealerships"
  add_foreign_key "customers", "dealerships"
  add_foreign_key "damage_reports", "dealership_vehicles", column: "vehicle_id"
  add_foreign_key "damage_reports", "drives"
  add_foreign_key "dealership_alerts", "dealerships"
  add_foreign_key "dealership_email_settings", "dealerships"
  add_foreign_key "dealership_features_settings", "dealerships"
  add_foreign_key "dealership_terms_settings", "dealerships"
  add_foreign_key "dealership_vehicles", "brands"
  add_foreign_key "dealership_vehicles", "dealerships"
  add_foreign_key "dealerships", "brands"
  add_foreign_key "dealerships", "dealership_groups"
  add_foreign_key "device_registrations", "users"
  add_foreign_key "drives", "customers"
  add_foreign_key "drives", "dealership_vehicles", column: "vehicle_id"
  add_foreign_key "drives", "dealerships"
  add_foreign_key "drives", "driver_licenses"
  add_foreign_key "drives", "trade_plates"
  add_foreign_key "drives", "users", column: "sales_person_accompanying_id"
  add_foreign_key "drives", "users", column: "sales_person_id"
  add_foreign_key "finance_details", "customer_vehicles"
  add_foreign_key "options_fitted", "customer_vehicles"
  add_foreign_key "reconditioning_costs", "vehicle_conditions"
  add_foreign_key "trade_plates", "dealerships"
  add_foreign_key "user_dealerships", "dealerships"
  add_foreign_key "user_dealerships", "users"
  add_foreign_key "vehicle_conditions", "customer_vehicles"
  add_foreign_key "vehicle_histories", "customer_vehicles"
end
