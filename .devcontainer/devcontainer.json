{"name": "Dealer Drive App Runner", "dockerComposeFile": ["../docker-compose.yml"], "service": "web", "workspaceFolder": "/dealer-drive-backend", "customizations": {"vscode": {"settings": {"terminal.integrated.defaultProfile.linux": "bash", "editor.formatOnSave": true, "editor.formatOnSaveTimeout": 5000, "ruby.format": "rubocop", "editor.codeActionsOnSave": {"source.fixAll.stylelint": "always"}, "css.validate": false, "less.validate": false, "scss.validate": false, "stylelint.syntax": "scss", "stylelint.validate": ["scss", "css", "sass"], "stylelint.snippet": ["scss"], "markdownlint.config": {"default": true, "no-duplicate-heading": {"siblings_only": true}, "no-hard-tabs": false}, "dev.containers.copyGitConfig": true, "git.enableCommitSigning": false}, "[ruby]": {"editor.defaultFormatter": "LoranKloeze.ruby-rubocop-revived"}, "extensions": ["eamodio.gitlens", "EditorConfig.EditorConfig", "redhat.vscode-yaml", "noku.rails-run-spec-vscode", "github.vscode-pull-request-github", "GitHub.copilot", "GitHub.copilot-chat", "Shopify.ruby-lsp", "LoranKloeze.ruby-rubocop-revived"]}}, "runServices": ["web"], "forwardPorts": [3000, 3306, 6379], "mounts": ["source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached", "source=${localEnv:HOME}/.gnupg,target=/home/<USER>/.gnupg,type=bind,consistency=cached"], "remoteEnv": {"GPG_TTY": "/dev/pts/0"}}