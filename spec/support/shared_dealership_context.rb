RSpec.shared_context "dealership_api_shared_context", shared_context: :metadata do
  include_context "users_api_shared_context"
  let(:toyota) { create(:brand, name: "Toyota #{SecureRandom.hex(4)}") }

  let(:dealership) { create(:dealership, brand: toyota) }
  let(:dealership_uuid) { dealership.uuid }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin) }
end
