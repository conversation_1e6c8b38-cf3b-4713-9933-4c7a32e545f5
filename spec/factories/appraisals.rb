 FactoryBot.define do
  factory :appraisal do
    association :dealership
    association :customer
    association :sales_person, factory: :user
    status { :incomplete }
    created_by { sales_person }
    updated_by { sales_person }
    completed_percentage { 0 }
    awarded_value { 10000.0 }
    price { 12000.0 }
    give_price { 11000.0 }
    awarded_notes { "Initial appraisal." }
    uuid { SecureRandom.uuid }
    created_at { Time.current }
    updated_at { Time.current }

    trait :with_vehicle do
      after(:create) do |appraisal|
        create(:customer_vehicle, appraisal: appraisal)
      end
    end
  end
end
