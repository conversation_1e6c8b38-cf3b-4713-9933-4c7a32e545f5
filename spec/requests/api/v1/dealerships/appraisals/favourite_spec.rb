require 'rails_helper'
require 'swagger_helper'

RSpec.describe 'Api::V1::Dealerships::AppraisalsController#favourite', type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: user) }



  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/favourite" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/favourite" }

    context "when authorized" do
      let(:request_headers) { headers }

      context "when adding to favourites" do
        it "creates a favourite record" do
          expect {
            put url, params: { status: true }, headers: request_headers
          }.to change(FavouriteAppraisal, :count).by(1)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal added to favourites")
          expect(json.dig("data", "favourite")).to be true
        end

        it "doesn't create duplicate favourite records" do
          create(:favourite_appraisal, user: user, appraisal: appraisal)

          expect {
            put url, params: { status: true }, headers: request_headers
          }.not_to change(FavouriteAppraisal, :count)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal added to favourites")
          expect(json.dig("data", "favourite")).to be true
        end
      end

      context "when removing from favourites" do
        before { create(:favourite_appraisal, user: user, appraisal: appraisal) }

        it "removes the favourite record" do
          expect {
            put url, params: { status: false }, headers: request_headers
          }.to change(FavouriteAppraisal, :count).by(-1)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal removed from favourites")
          expect(json.dig("data", "favourite")).to be false
        end

        it "handles removing non-existent favourite gracefully" do
          FavouriteAppraisal.destroy_all

          expect {
            put url, params: { status: false }, headers: request_headers
          }.not_to change(FavouriteAppraisal, :count)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal removed from favourites")
          expect(json.dig("data", "favourite")).to be false
        end
      end

      context "with invalid parameters" do
        it "returns error when status is missing" do
          put url, params: {}, headers: request_headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("status parameter is required")
        end

        it "returns error when status is invalid" do
          put url, params: { status: "maybe" }, headers: request_headers

          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("status must be true or false")
        end
      end
    end

    context "when unauthorized" do
      it "returns unauthorized" do
        put url, params: { status: true }

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when appraisal not found" do
      let(:request_headers) { headers }

      it "returns not found" do
        put "/api/v1/dealerships/#{dealership.uuid}/appraisals/non-existent/favourite",
            params: { status: true }, headers: request_headers

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
