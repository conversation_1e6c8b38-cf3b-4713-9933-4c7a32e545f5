require 'rails_helper'
require 'swagger_helper'

RSpec.describe 'GET /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid', type: :request do
  include_context "appraisal_api_shared_context"

  let(:customer_vehicle) { create(:customer_vehicle, customer: customer, brand: toyota, rego: 'ABC123', make: 'Toyota', model: 'Camry') }
  let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, customer_vehicle: customer_vehicle, sales_person: sales_person, status: :complete, completed_percentage: 85, awarded_value: 25000, price: 30000, notes: 'Test notes') }

  describe 'GET /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid' do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}", headers: headers }

    context 'with valid authentication' do
      it 'returns the specific appraisal' do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to eq('Appraisal retrieved successfully')

        appraisal_data = json.dig('data', 'appraisal')
        expect(appraisal_data['uuid']).to eq(appraisal.uuid)
        expect(appraisal_data['status']).to eq('complete')
        expect(appraisal_data['completed_percentage']).to eq(85)
        expect(appraisal_data['awarded_value']).to eq("25000.0")
        expect(appraisal_data['price']).to eq("30000.0")
        expect(appraisal_data['notes']).to eq('Test notes')
      end

      it 'includes all associations' do
        subject

        json = response.parsed_body
        appraisal_data = json.dig('data', 'appraisal')

        # Customer data
        expect(appraisal_data['customer']['uuid']).to eq(customer.uuid)
        expect(appraisal_data['customer']['first_name']).to eq(customer.first_name)
        expect(appraisal_data['customer']['last_name']).to eq(customer.last_name)

        # Vehicle data
        expect(appraisal_data['vehicle']['uuid']).to eq(customer_vehicle.uuid)
        expect(appraisal_data['vehicle']['make']).to eq('Toyota')
        expect(appraisal_data['vehicle']['model']).to eq('Camry')
        expect(appraisal_data['vehicle']['rego']).to eq('ABC123')

        # Brand data
        expect(appraisal_data['vehicle']['brand']['uuid']).to eq(toyota.uuid)
        expect(appraisal_data['vehicle']['brand']['name']).to eq(toyota.name)

        # Sales person data
        expect(appraisal_data['sales_person']['uuid']).to eq(sales_person.uuid)

        # Dealership data
        expect(appraisal_data['dealership']['uuid']).to eq(dealership.uuid)
        expect(appraisal_data['dealership']['name']).to eq(dealership.name)
      end

      it 'includes timestamps' do
        subject

        json = response.parsed_body
        appraisal_data = json.dig('data', 'appraisal')

        expect(appraisal_data['created_at']).to be_present
        expect(appraisal_data['updated_at']).to be_present
      end
    end

    context 'with non-existent appraisal' do
      subject { get "/api/v1/dealerships/#{dealership.uuid}/appraisals/non-existent-uuid", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context 'with deleted appraisal' do
      let!(:deleted_appraisal) { create(:appraisal, dealership: dealership, customer: customer, status: :deleted, customer_vehicle: nil) }
      subject { get "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{deleted_appraisal.uuid}", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context 'with invalid dealership' do
      subject { get "/api/v1/dealerships/invalid-uuid/appraisals/#{appraisal.uuid}", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'without authentication' do
      let(:headers) { {} }

      it 'returns unauthorized' do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger Documentation
  path '/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
    parameter name: 'appraisal_uuid', in: :path, type: :string, description: 'Appraisal UUID'

    get 'Retrieve a specific appraisal' do
      tags 'Appraisals'
      description 'Retrieves detailed information about a specific appraisal including customer, vehicle, and dealership data'
      operationId 'getAppraisal'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device identifier'

      response '200', 'Appraisal retrieved successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisal retrieved successfully' }
                   },
                   required: %w[code message]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: %w[incomplete complete awarded archived deleted], example: 'complete' },
                         completed_percentage: { type: :integer, minimum: 0, maximum: 100, example: 85 },
                         awarded_value: { type: :string, example: '25000.0', description: 'Decimal value as string' },
                         price: { type: :string, example: '30000.0', description: 'Decimal value as string' },
                         give_price: { type: :string, example: '28000.0', description: 'Decimal value as string', nullable: true },
                         awarded_notes: { type: :string, example: 'Excellent condition vehicle', nullable: true },
                         notes: { type: :string, example: 'Additional appraisal notes', nullable: true }
                       }
                     }
                   }
                 }
               }

        let(:appraisal_uuid) { appraisal.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(200)
          expect(data['status']['message']).to eq('Appraisal retrieved successfully')
          expect(data['data']['appraisal']['uuid']).to eq(appraisal.uuid)
          expect(data['data']['appraisal']['status']).to eq('complete')
        end
      end

      response '404', 'Appraisal not found' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Appraisal not found' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:appraisal_uuid) { 'non-existent-uuid' }

        run_test!
      end

      response '401', 'Unauthorized' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:appraisal_uuid) { appraisal.uuid }
        let(:Authorization) { nil }
        let(:'Device-ID') { nil }

        run_test!
      end
    end
  end
end
