require "rails_helper"

RSpec.describe Dealership, type: :model do
  describe "associations" do
    it { should belong_to(:dealership_group).optional }
    it { should belong_to(:brand).optional }
    it { should have_one(:dealership_features_setting) }
    it { should have_one(:dealership_email_setting) }
    it { should have_one(:dealership_terms_setting) }
    it { should have_one_attached(:logo) }
    it { should have_many(:customers).dependent(:destroy) }
    it { should have_many(:vehicles).class_name('DealershipVehicle').dependent(:destroy) }
    it { should have_many(:trade_plates).dependent(:destroy) }
    it { should have_many(:drives).dependent(:destroy) }
    it { should have_many(:dealership_alerts) }
    it { should have_many(:appraisals).dependent(:destroy) }
    it { should have_many(:customers).dependent(:destroy) }
    it { should have_many(:drives).dependent(:destroy) }
    it { should have_many(:user_dealerships).dependent(:destroy) }
    it { should have_many(:users).through(:user_dealerships) }
  end

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_presence_of(:address_line1) }
    it { is_expected.to validate_presence_of(:state) }
    it { is_expected.to validate_presence_of(:postcode) }
    it { is_expected.to validate_presence_of(:country) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_presence_of(:phone) }

    describe "website" do
      it "is valid with a proper URL format" do
        dealership = build(:dealership, website: "https://example.com")
        expect(dealership).to be_valid
      end

      it "is valid with a blank website" do
        dealership = build(:dealership, website: "")
        expect(dealership).to be_valid
      end

      it "is valid with a nil website" do
        dealership = build(:dealership, website: nil)
        expect(dealership).to be_valid
      end

      it "is invalid with an improper URL format" do
        dealership = build(:dealership, website: "not-a-url")
        expect(dealership).not_to be_valid
        expect(dealership.errors[:website]).to include("is not a valid URL")
      end
    end

    describe "uuid" do
      it "automatically sets a UUID as before_hook before doing validation on create" do
        dealership = create(:dealership)
        expect(dealership).to be_valid
        expect(dealership.uuid).to be_present
        expect(dealership.uuid).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
      end

      it "does not change UUID on update" do
        dealership = create(:dealership)
        original_uuid = dealership.uuid
        dealership.name = "New Name"
        expect(dealership).to be_valid
        expect(dealership.uuid).to eq(original_uuid)
      end

      it "enforces uniqueness of uuid" do
        existing_dealership = create(:dealership)
        new_dealership = build(:dealership, uuid: existing_dealership.uuid)
        expect(new_dealership).not_to be_valid
        expect(new_dealership.errors[:uuid]).to include("has already been taken")
      end
    end

    describe "email format" do
      it "is valid with a proper email format" do
        dealership = build(:dealership, email: "<EMAIL>")
        expect(dealership).to be_valid
      end

      it "is invalid with an improper email format" do
        dealership = build(:dealership, :with_invalid_email)
        expect(dealership).not_to be_valid
        expect(dealership.errors[:email]).to include("invalid-email is not a valid email")
      end

      it "is invalid with an email longer than 255 characters" do
        long_email = "a" * 256 + "@example.com"
        dealership = build(:dealership, email: long_email)
        expect(dealership).not_to be_valid
        expect(dealership.errors[:email]).to include("#{long_email} must be under 255 characters")
      end
    end

    describe "logo attachment" do
      it "is valid with a proper image file" do
        dealership = build(:dealership)
        dealership.logo.attach(
          io: StringIO.new(Rails.root.join("spec/fixtures/files/test_logo.png").binread),
          filename: "test_logo.png",
          content_type: "image/png"
        )
        expect(dealership).to be_valid
      end

      it "is invalid with an improper file type" do
        dealership = build(:dealership)
        dealership.logo.attach(
          io: StringIO.new("some text content"),
          filename: "test.txt",
          content_type: "text/plain"
        )
        expect(dealership).not_to be_valid
        expect(dealership.errors[:logo]).to include("has an invalid content type (authorized content types are PNG, JPG)")
      end

      it "is invalid with a file larger than 5MB" do
        dealership = build(:dealership)
        # Create a 6MB string of random data
        large_content = SecureRandom.bytes(6.megabytes)
        dealership.logo.attach(
          io: StringIO.new(large_content),
          filename: "large_logo.png",
          content_type: "image/png"
        )
        expect(dealership).not_to be_valid
        expect(dealership.errors[:logo]).to include("file size must be less than 5 MB (current size is 6 MB)")
      end
    end

    describe "phone" do
      context "with Australian numbers" do
        it "is valid with a proper Australian mobile number" do
          dealership = build(:dealership, country: :au, phone: "+**********8")
          expect(dealership).to be_valid
        end

        it "is valid with a proper Australian landline number" do
          dealership = build(:dealership, country: :au, phone: "+***********")
          expect(dealership).to be_valid
        end

        it "is invalid with an incorrect Australian number" do
          dealership = build(:dealership, country: :au, phone: "+**********")
          expect(dealership).not_to be_valid
          expect(dealership.errors[:phone]).to include("is invalid")
        end
      end

      context "with US numbers" do
        it "is valid with a proper US mobile number" do
          dealership = build(:dealership, country: :us, phone: "+*********34")
          expect(dealership).to be_valid
        end

        it "is valid with a proper US landline number" do
          dealership = build(:dealership, country: :us, phone: "+*********34")
          expect(dealership).to be_valid
        end

        it "is invalid with an incorrect US number" do
          dealership = build(:dealership, country: :us, phone: "+*********")
          expect(dealership).not_to be_valid
          expect(dealership.errors[:phone]).to include("is invalid")
        end
      end

      it "is invalid with a blank number" do
        dealership = build(:dealership, phone: "")
        expect(dealership).not_to be_valid
        expect(dealership.errors[:phone]).to include("can't be blank")
      end

      it "is invalid with nil" do
        dealership = build(:dealership, phone: nil)
        expect(dealership).not_to be_valid
        expect(dealership.errors[:phone]).to include("can't be blank")
      end
    end
  end

  describe "phone number formatting" do
    it "stores phone numbers in E.164 format" do
      dealership = create(:dealership, country: :au, phone: "0412 345 678")
      expect(dealership.phone).to eq("+**********8")
    end

    it "formats phone numbers in national format" do
      dealership = create(:dealership, country: :au, phone: "+**********8")
      expect(dealership.formatted_phone).to eq("0412 345 678")
    end

    it "handles blank values" do
      dealership = build(:dealership, phone: "")
      expect(dealership.phone).to be_nil
    end
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:status).with_values(active: 0, suspended: 1, deleted: 2).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:country).with_values(au: "Australia", us: "USA").backed_by_column_of_type(:string) }
    it { is_expected.to define_enum_for(:setting_date_format).with_values(dd_mm_yyyy: 0, mm_dd_yyyy: 1, yyyy_mm_dd: 2).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:setting_time_zone).with_values(utc: "UTC", perth: "Perth", sydney: "Sydney", melbourne: "Melbourne", brisbane: "Brisbane", adelaide: "Adelaide").backed_by_column_of_type(:string) }
    it { is_expected.to define_enum_for(:setting_distance_unit).with_values(kilometers: 0, miles: 1).backed_by_column_of_type(:integer) }
  end

  describe "default values" do
    it "sets default status to active" do
      dealership = Dealership.new
      expect(dealership.status).to eq("active")
    end

    it "sets default country to au" do
      dealership = Dealership.new
      expect(dealership.country).to eq("au")
    end

    it "sets default setting_date_format to dd_mm_yyyy" do
      dealership = Dealership.new
      expect(dealership.setting_date_format).to eq("dd_mm_yyyy")
    end

    it "sets default setting_time_zone to utc" do
      dealership = Dealership.new
      expect(dealership.setting_time_zone).to eq("utc")
    end

    it "sets default setting_distance_unit to kilometers" do
      dealership = Dealership.new
      expect(dealership.setting_distance_unit).to eq("kilometers")
    end
  end

  describe "traits" do
    describe "with_logo" do
      it "attaches a logo to the dealership" do
        dealership = create(:dealership, :with_logo)
        expect(dealership.logo).to be_attached
      end
    end

    describe "without_logo" do
      it "is valid without a logo" do
        dealership = create(:dealership, :without_logo)
        expect(dealership).to be_valid
      end
    end

    describe "without_brand" do
      it "is valid without a brand" do
        dealership = create(:dealership, :without_brand)
        expect(dealership).to be_valid
      end
    end
  end

  describe "callbacks" do
    describe "before_validation" do
      it "automatically sets a UUID as before_hook before doing validation on create" do
        dealership = build(:dealership)
        expect(dealership.uuid).to be_nil
        dealership.valid?
        expect(dealership.uuid).to be_present
        expect(dealership.uuid).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
      end

      it "does not change UUID on update" do
        dealership = create(:dealership)
        original_uuid = dealership.uuid
        dealership.name = "New Name"
        dealership.valid?
        expect(dealership.uuid).to eq(original_uuid)
      end

      it "generates unique UUIDs" do
        dealership1 = create(:dealership)
        dealership2 = create(:dealership)
        expect(dealership1.uuid).not_to eq(dealership2.uuid)
      end
    end
  end

  describe "scopes" do
    describe ".active" do
      it "returns only active dealerships" do
        active_dealership = create(:dealership, status: :active)
        suspended_dealership = create(:dealership, status: :suspended)
        deleted_dealership = create(:dealership, status: :deleted)

        expect(Dealership.active).to include(active_dealership)
        expect(Dealership.active).not_to include(suspended_dealership)
        expect(Dealership.active).not_to include(deleted_dealership)
      end
    end

    describe ".suspended" do
      it "returns only suspended dealerships" do
        active_dealership = create(:dealership, status: :active)
        suspended_dealership = create(:dealership, status: :suspended)
        deleted_dealership = create(:dealership, status: :deleted)

        expect(Dealership.suspended).to include(suspended_dealership)
        expect(Dealership.suspended).not_to include(active_dealership)
        expect(Dealership.suspended).not_to include(deleted_dealership)
      end
    end

    describe ".deleted" do
      it "returns only deleted dealerships" do
        active_dealership = create(:dealership, status: :active)
        suspended_dealership = create(:dealership, status: :suspended)
        deleted_dealership = create(:dealership, status: :deleted)

        expect(Dealership.deleted).to include(deleted_dealership)
        expect(Dealership.deleted).not_to include(active_dealership)
        expect(Dealership.deleted).not_to include(suspended_dealership)
      end
    end
  end

  describe "#managers" do
    let(:dealership) { create(:dealership) }
    let(:admin_user) { create(:user) }
    let(:sales_user) { create(:user) }

    before do
      create(:user_dealership, user: admin_user, dealership: dealership, role: :dealership_admin)
      create(:user_dealership, user: sales_user, dealership: dealership, role: :sales_person)
    end

    it "returns only admin users" do
      expect(dealership.managers).to include(admin_user)
      expect(dealership.managers).not_to include(sales_user)
    end
  end

  describe "#sales_people" do
    let(:dealership) { create(:dealership) }
    let(:admin_user) { create(:user) }
    let(:sales_user) { create(:user) }

    before do
      create(:user_dealership, user: admin_user, dealership: dealership, role: :dealership_admin)
      create(:user_dealership, user: sales_user, dealership: dealership, role: :sales_person)
    end

    it "returns only sales users" do
      expect(dealership.sales_people).to include(sales_user)
      expect(dealership.sales_people).not_to include(admin_user)
    end
  end
end
