require 'rails_helper'

RSpec.describe Vehicle::ReconditioningCost, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition).class_name('Vehicle::VehicleCondition') }
  end

  describe 'enums' do
    it do
      should define_enum_for(:cost_type).
        with_values(
          paint_and_panel: 0,
          wheels_and_tyres: 1,
          windscreen: 2,
          mechanical: 3,
          registration: 4,
          other: 5
        )
    end
  end

  describe 'validations' do
    subject { FactoryBot.build(:reconditioning_cost) }

    it { is_expected.to validate_presence_of(:cost_type) }
    it { is_expected.to validate_presence_of(:amount_cents) }
    it { is_expected.to validate_numericality_of(:amount_cents).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_presence_of(:currency) }
  end
end
