require 'rails_helper'

RSpec.describe Vehicle::ComponentRating, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition) }
  end

  describe 'validations' do
    subject { FactoryBot.build(:component_rating) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:rating) }
    it { is_expected.to validate_numericality_of(:rating).only_integer.is_greater_than_or_equal_to(1).is_less_than_or_equal_to(5) }
  end

  describe 'enum' do
    it 'defines the correct enum values for name' do
      expect(described_class.names.keys).to match_array(%w[
        front_wheels rear_wheels panel_work paint_work interior windscreen mechanical
      ])
    end

    it 'provides enum helper methods' do
      rating = FactoryBot.build(:component_rating, name: :front_wheels)
      expect(rating.front_wheels?).to be true
    end
  end
end
