module Vehicle
  class ReconditioningCost < ApplicationRecord
    belongs_to :vehicle_condition, class_name: "Vehicle::VehicleCondition"

    enum :cost_type, {
      paint_and_panel: 0,
      wheels_and_tyres: 1,
      windscreen: 2,
      mechanical: 3,
      registration: 4,
      other: 5
    }

    validates :cost_type, presence: true
    validates :amount_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :currency, presence: true
  end
end
