json.extract! appraisal, :uuid, :status, :completed_percentage, :awarded_value, :price, :give_price, :awarded_notes, :notes

json.favourite appraisal.favourited_by?(@current_user)

json.dealership do
  json.extract! appraisal.dealership, :uuid, :name, :long_name
end

json.customer do
  json.extract! appraisal.customer, :uuid, :first_name, :last_name, :email, :phone_number, :full_name, :gender, :age,
                                    :company_name, :external_id, :postcode, :suburb, :address_line1, :address_line2, :city, :state, :country
end

json.sales_person do
  json.extract! appraisal.sales_person, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

json.created_by do
  json.extract! appraisal.created_by, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

json.updated_by do
  json.extract! appraisal.updated_by, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

if appraisal.customer_vehicle
  json.partial! "api/v1/dealerships/appraisals/customer_vehicle_full", customer_vehicle: appraisal.customer_vehicle
end

json.created_at format_iso8601_with_offset(appraisal.created_at)
json.updated_at format_iso8601_with_offset(appraisal.updated_at)
