json.vehicle do
  # Extract basic attributes
  json.extract! customer_vehicle,
    :uuid, :make, :model, :rego, :build_year, :build_month, :exterior_color, :interior_color,
    :fuel_type, :odometer_reading

  # Attachment URLs
  json.main_photo_url customer_vehicle.main_photo.attached? ? customer_vehicle.main_photo.url : nil

  # Brand data
  if customer_vehicle.brand.present?
    json.brand do
      json.extract! customer_vehicle.brand, :uuid, :name, :logo_url
    end
  end
end
