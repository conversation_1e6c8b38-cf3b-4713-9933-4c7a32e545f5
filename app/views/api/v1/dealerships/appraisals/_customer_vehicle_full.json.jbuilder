json.vehicle do
  # Extract basic attributes
  json.extract! customer_vehicle,
    :uuid, :make, :model, :vin, :rego, :registration_expiry,
    :registration_state, :build_year, :build_month, :compliance_year,
    :compliance_month, :exterior_color, :interior_color, :seat_type,
    :fuel_type, :driving_wheels, :spare_wheel_type, :transmission,
    :body_type, :number_of_doors, :number_of_seats, :engine_kilowatts,
    :engine_number, :wheel_size_front, :wheel_size_rear, :odometer_reading,
    :odometer_date, :redbook_code, :is_vehicle_present

  # Attachment URLs
  json.main_photo_url customer_vehicle.main_photo.attached? ? customer_vehicle.main_photo.url : nil
  json.odometer_reading_photo_url customer_vehicle.odometer_reading_photo.attached? ? customer_vehicle.odometer_reading_photo.url : nil

  # Photos array
  json.photo_urls customer_vehicle.photos.map { |photo| photo.url } if customer_vehicle.photos.attached?

  # Brand data
  if customer_vehicle.brand.present?
    json.brand do
      json.extract! customer_vehicle.brand, :uuid, :name, :logo_url
    end
  end
end
