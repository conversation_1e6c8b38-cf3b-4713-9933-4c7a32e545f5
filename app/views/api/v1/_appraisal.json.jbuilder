json.extract! appraisal, :uuid, :status, :completed_percentage, :awarded_value, :price, :give_price

json.customer do
  json.extract! appraisal.customer, :uuid, :first_name, :last_name, :email, :phone_number, :full_name
end

json.sales_person do
  json.extract! appraisal.sales_person, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

if appraisal.customer_vehicle
  json.partial! "api/v1/dealerships/appraisals/customer_vehicle", customer_vehicle: appraisal.customer_vehicle
end

json.favourite appraisal.favourited_by?(@current_user)

json.created_at format_iso8601_with_offset(appraisal.created_at)
json.updated_at format_iso8601_with_offset(appraisal.updated_at)
