services:
  db:
    image: mysql:9.3
    restart: always
    environment:
      MYSQL_DATABASE: dealer-drive-backend_development
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  web:
    build: .
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -b 0.0.0.0"
    volumes:
      - .:/dealer-drive-backend
      - ~/.ssh:/root/.ssh:ro
      - ~/.gnupg:/root/.gnupg:ro
    ports:
      - "3000:3000"
    environment:
      REDIS_URL: redis://redis:6379
      GPG_TTY: /dev/pts/0
      RAILS_ENV: development
    depends_on:
      - db
      - redis
    # Add these lines for GPG to work properly
    tty: true
    stdin_open: true

  sidekiq:
    build: .
    command: bundle exec sidekiq
    volumes:
      - .:/dealer-drive-backend
      - ~/.ssh:/root/.ssh:ro
      - ~/.gnupg:/root/.gnupg:ro
    environment:
      REDIS_URL: redis://redis:6379
      GPG_TTY: /dev/pts/0
    depends_on:
      - db
      - redis

volumes:
  db_data:
