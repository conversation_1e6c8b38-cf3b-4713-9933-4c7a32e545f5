FROM ruby:3.4

RUN apt-get update -qq && apt-get install -yq --no-install-recommends \
  build-essential \
  gnupg2 \
  curl \
  less \
  git \
  libvips \
  libpq-dev \
  default-mysql-client \
  redis-tools \
  vim \
  libssl-dev \
  libghc-zlib-dev \
  libcurl4-gnutls-dev \
  libexpat1-dev \
  libgnutls30 \
  openssh-client \
  libvips42 \
  default-libmysqlclient-dev \
  && apt-get clean \
  && rm -rf /var/cache/apt/archives/* \
  && truncate -s 0 /var/log/*log \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*


# Add Node.js to sources list
RUN curl -sL https://deb.nodesource.com/setup_22.x | bash -

# Install Node.js version that will enable installation of yarn
RUN apt-get install -y --no-install-recommends \
  nodejs \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*


# set the timezone to Australia/Sydney
ENV TZ=Australia/Sydney
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Enforce cachebusting
ENV version=2

RUN npm install -g yarn

WORKDIR /dealer-drive-backend

RUN cd /dealer-drive-backend && \
  yarn install

COPY Gemfile* ./
RUN bundle install

COPY . .

# Set up GPG for non-interactive use
RUN echo 'export GPG_TTY=$(tty)' >> ~/.bashrc

EXPOSE 3000

CMD ["bin/rails", "server", "-b", "0.0.0.0"]
